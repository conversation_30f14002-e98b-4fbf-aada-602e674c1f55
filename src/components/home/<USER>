'use client';

import Image from 'next/image';
import Link from 'next/link';

// Partner logos data
const financingPartners = [
  { name: 'QIB', src: '/home/<USER>', width: 80, height: 40 },
  { name: 'Commercial Bank', src: '/home/<USER>', width: 120, height: 40 },
];

const technologyPartners = [
  { name: 'Fundfina', src: '/home/<USER>', width: 100, height: 40, href: "https://www.fundfina.com" },
  { name: 'Shufti', src: '/home/<USER>', width: 90, height: 40, href: "https://shuftipro.com" },
  { name: 'QatarSMS', src: '/home/<USER>', width: 110, height: 40, href: "https://www.qatarsms.net" },
];

export const HeroSection = () => {
  return (
    <section className="bg-white pt-16 lg:pt-24 pb-8">
      <div className="container mx-auto px-4 lg:px-8 flex flex-col items-center text-center">

        {/* --- Top Text Content --- */}
        <div className="max-w-4xl">
          <h1 className="text-[2.60rem] font-extrabold text-[#111827] leading-tight mb-2">
            Turn Invoices into Cash – Instantly
          </h1>
          <p className="text-lg md:text-3xl text-dark-green mb-2 font-semibold">
            Struggling with payments and tight cash flow?
          </p>
          <p className="text-base md:text-xl text-light-green mb-8">
            Free up working capital instantly by discounting your unpaid invoices
          </p>
        </div>

        {/* --- CTA Button --- */}
        <div className="mb-12 mt-4 hover:scale-105 duration-100">
            <Link
              href="https://madad-msme.fundfina.com" // Pointing the button to the register page
              className="bg-primary text-white px-6 py-4 rounded-lg font-semibold transition-colors text-lg shadow-md hover:shadow-lg"
            >
              Get Started
            </Link>
        </div>

        {/* --- Hero Image --- */}
        <div className="w-full max-w-4xl mb-10">
          <Image 
            src="/home/<USER>" 
            alt="Illustration of invoice discounting process" 
            width={800}
            height={450}
            priority
            className="w-full h-auto"
          />
        </div>

        <hr className='w-screen h-px border-[#E1E1E1]'/>
        {/* --- Partners Section --- */}
        <div className="w-full max-w-5xl mt-8">
            <p className="text-lg text-primary font-bold mb-6">
                Proud participant in Qatar Central Bank&apos;s Sandbox, backed by trusted partners
            </p>
            <div className="flex flex-col md:flex-row items-center justify-center gap-10 md:gap-12 lg:gap-16">
                
                {/* Financing Partners */}
                <div className="flex flex-col items-center">
                    <h3 className="text-sm font-extrabold text-dark-text tracking-widest uppercase mb-4">
                        FINANCING PARTNER
                    </h3>
                    <div className="flex items-center gap-8">
                        {financingPartners.map(partner => (
                            <Image
                                key={partner.name}
                                src={partner.src}
                                alt={`${partner.name} Logo`}
                                width={partner.width}
                                height={partner.height}
                                className="object-contain"
                            />
                        ))}
                    </div>
                </div>

                {/* Vertical Separator */}
                <div className="hidden md:block w-px bg-gray-200 self-stretch mx-20"></div>

                {/* Technology Partners */}
                <div className="flex flex-col items-center">
                    <h3 className="text-sm font-extrabold text-dark-text tracking-widest uppercase mb-4">
                        TECHNOLOGY PARTNER
                    </h3>
                    <div className="flex items-center flex-wrap justify-center gap-8">
                        {technologyPartners.map(partner => (
                            partner.href ? (
                                <a
                                    key={partner.name}
                                    href={partner.href}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    className="cursor-pointer hover:opacity-80 transition-opacity"
                                >
                                    <Image
                                        src={partner.src}
                                        alt={`${partner.name} Logo`}
                                        width={partner.width}
                                        height={partner.height}
                                        className="object-contain"
                                    />
                                </a>
                            ) : (
                                <Image
                                    key={partner.name}
                                    src={partner.src}
                                    alt={`${partner.name} Logo`}
                                    width={partner.width}
                                    height={partner.height}
                                    className="object-contain"
                                />
                            )
                        ))}
                    </div>
                </div>
            </div>
        </div>

      </div>
    </section>
  );
};
