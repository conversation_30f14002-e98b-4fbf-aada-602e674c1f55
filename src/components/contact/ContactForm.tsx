'use client';

import { useState } from 'react';

// Form data interface
interface FormData {
  name: string;
  phoneNumber: string;
  email: string;
  reasonToContact: string;
  message: string;
}

export const ContactForm = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    phoneNumber: '',
    email: '',
    reasonToContact: '',
    message: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error('Failed to send message.');
      }

      setSubmitStatus('success');
      // Reset form on success
      setFormData({ name: '', phoneNumber: '', email: '', reasonToContact: '', message: '' });
    } catch (error) {
      console.error(error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="bg-gray-50">
      <div className="mx-auto">
        <div className="mx-auto grid grid-cols-1 lg:grid-cols-5 overflow-hidden">
          
          {/* Left Column: Contact Details */}
          <div className="lg:col-span-2 bg-[#0D1A26] text-white p-8 sm:p-12 lg:p-16 flex flex-col justify-center">
            <div className="space-y-12 text-[18px]">
              {/* Registered Address */}
              <div>
                <h2 className="text-2xl font-bold mb-3">Registered Address</h2>
                <p className="text-white leading-relaxed">
                  Building No. 98, Zone 61, Street 820,
                  <br />
                  P.O. Box 23245,
                  <br />
                  Doha, Qatar
                </p>
              </div>

              {/* Happy to Connect - This section is now corrected */}
              <div>
                <h2 className="text-2xl font-bold mb-4">Happy to Connect</h2>
                <div className="space-y-6">
                  <div>
                    <h3 className="text-white">Officer Name</h3>
                    <p className="text-white font-bold">Paolo Abellanosa</p>
                  </div>
                  <div>
                    <h3 className="text-white">Contact Number</h3>
                    <a href="mailto:<EMAIL>" className="block text-white font-bold hover:text-white transition">
                      <EMAIL>
                    </a>
                    <a href="tel:+97433835163" className="block text-white font-bold hover:text-white transition">
                      +974 3383 5163
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column: Contact Form */}
          <div className="lg:col-span-3 bg-white p-8 sm:p-12">
            <h2 className="text-2xl font-bold text-[#161C2D] mb-8">Send us a message</h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              
              {/* Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-bold text-gray-700 mb-1">Name</label>
                <input
                  type="text" id="name" name="name" value={formData.name} onChange={handleInputChange}
                  placeholder="i.e. John Doe" required
                  className="w-full p-3 bg-white border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                />
              </div>
              
              {/* Phone and Email Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label htmlFor="phoneNumber" className="block text-sm font-bold text-gray-700 mb-1">Phone Number</label>
                    <input
                      type="tel" id="phoneNumber" name="phoneNumber" value={formData.phoneNumber} onChange={handleInputChange}
                      placeholder="i.e. ******-567-7890" required
                      className="w-full p-3 bg-white border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                    />
                </div>
                <div>
                    <label htmlFor="email" className="block text-sm font-bold text-gray-700 mb-1">Email</label>
                    <input
                      type="email" id="email" name="email" value={formData.email} onChange={handleInputChange}
                      placeholder="i.e. <EMAIL>" required
                      className="w-full p-3 bg-white border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                    />
                </div>
              </div>

              {/* Reason to Contact */}
              <div>
                <label htmlFor="reasonToContact" className="block text-sm font-bold text-gray-700 mb-1">Reason to Contact</label>
                <input
                  type="text" id="reasonToContact" name="reasonToContact" value={formData.reasonToContact} onChange={handleInputChange}
                  placeholder="i.e. I need a help" required
                  className="w-full p-3 bg-white border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
                />
              </div>
              
              {/* Message */}
              <div>
                <label htmlFor="message" className="block text-sm font-bold text-gray-700 mb-1">Message</label>
                <textarea
                  id="message" name="message" value={formData.message} onChange={handleInputChange}
                  placeholder="Type your message" required rows={4}
                  className="w-full p-3 bg-white border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors resize-none"
                />
              </div>
              
              {/* Submit Button */}
              <div>
                <button
                  type="submit" disabled={isSubmitting}
                  className="w-auto bg-light-green  disabled:bg-gray-400 text-white font-semibold py-4 px-8 rounded-md transition-colors"
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </button>
              </div>

              {/* Status Messages */}
              {submitStatus === 'success' && (
                <div className="p-4 text-light-green border border-green-200 rounded-lg bg-green-50">
                  Thank you for your message! We&apos;ll get back to you soon.
                </div>
              )}
              {submitStatus === 'error' && (
                <div className="p-4 text-red-700 border border-red-200 rounded-lg bg-red-50">
                  Sorry, there was an error sending your message. Please try again.
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};