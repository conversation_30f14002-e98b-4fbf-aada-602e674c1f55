'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/solid';

// --- DATA SOURCE FOR THE CAROUSEL ---
const principles = [
  {
    title: 'Empowering SMEs',
    description: 'We are committed to supporting Micro, Small, and Medium Enterprises as the backbone of the economy. Empowering entrepreneurs drives our purpose and inspires our work every day. We believe that our success lies in their success.',
    imageSrc: '/about/slider_1.jpg',
    imageAlt: 'Empowering SMEs',
  },
  {
    title: 'Innovation with impact',
    description: 'Innovation drives our solutions. Our focus on impactful innovation ensures our technology and offerings remain relevant and effective.',
    imageSrc: '/about/slider_2.jpg',
    imageAlt: 'Innovation with impact',
  },
  {
    title: 'Trust and Transparency',
    description: 'We foster trust by prioritizing transparent operations, ethical practices, and compliance with regulatory standards. Businesses can rely on us for clarity, fairness, and a secure financial partnership.',
    imageSrc: '/about/slider_3.png',
    imageAlt: 'Trust and Transparency',
  },
  {
    title: 'Agility and Growth',
    description: 'We adapt quickly to changes in the market while staying focused on achieving ambitious goals. This culture of agility ensures our platform evolves alongside the needs of MSMEs and financial partners.',
    imageSrc: '/about/slider_4.jpg',
    imageAlt: 'Agility and Growth',
  },
  {
    title: 'Focus on Ecosystem',
    description: 'Madad thrives on partnerships—with SMEs, financial institutions, and other stakeholders in the ecosystem. Together, we create value and build a thriving financial network that benefits all.',
    imageSrc: '/about/slider_5.jpg',
    imageAlt: 'Focus on Ecosystem',
  },
  {
    title: 'Human-Centric Approach',
    description: 'Our solutions are designed with people at the core, focusing on real-world problems SMEs face. Empathy and understanding guide how we design and deliver services, ensuring they make a meaningful difference.',
    imageSrc: '/about/slider_6.jpg',
    imageAlt: 'Human-Centric Approach',
  }
];

export const CorePrinciplesSection = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // --- HANDLER FUNCTIONS ---
  const handleNext = () => {
    const isLastSlide = currentIndex === principles.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };
  
  const handlePrev = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? principles.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToSlide = (slideIndex: number) => {
    setCurrentIndex(slideIndex);
  };
  
  // --- AUTO-PLAY EFFECT ---
  useEffect(() => {
    const intervalId = setInterval(() => {
      handleNext();
    }, 5000);
    return () => clearInterval(intervalId);
  }, [currentIndex]); 

  const currentPrinciple = principles[currentIndex];

  return (
    <section className="bg-[#F3F9F9]">
      <div className="mx-auto">
        {/* === CHANGE IS HERE === */}
        {/* Added h-[545px] to enforce a fixed height on the grid container */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center h-[545px]">
          {/* Left Column: Image */}
         <div className="relative aspect-[4/3] overflow-hidden lg:aspect-auto lg:h-full">
            <Image
              key={currentIndex}
              src={currentPrinciple.imageSrc}
              alt={currentPrinciple.imageAlt}
              fill
              sizes="(max-width: 1024px) 100vw, 40vw"
              className="object-cover animate-fadeIn"
            />
          </div>

          {/* Right Column: Content */}
          <div className="flex flex-col h-full pr-8 pt-8 lg:pt-28 px-16 lg:px-0 pb-8 lg:pb-0 lg:pr-10">
            <h2 className="text-4xl font-extrabold text-light-green mb-8 lg:mb-24">
              Core Cultural Principles
            </h2>
            
            <div key={`${currentIndex}-text`} className="animate-fadeIn">
              <h3 className="text-2xl font-bold text-gray-800 mb-3">
                {currentPrinciple.title}
              </h3>
              <p className="text-[#48484A] leading-relaxed flex-grow">
                {currentPrinciple.description}
              </p>
            </div>

            {/* Carousel Controls */}
            <div className="flex items-center justify-between mt-10">
              <div className="flex items-center space-x-1">
                {principles.map((_, slideIndex) => (
                  <div
                    key={slideIndex}
                    onClick={() => goToSlide(slideIndex)}
                    className={`w-2.5 h-2.5 rounded-full cursor-pointer transition-colors ${
                      currentIndex === slideIndex ? 'bg-[#507878]' : 'bg-[#CFDFDF]'
                    }`}
                  ></div>
                ))}
              </div>
              
              <div className="flex items-center space-x-8">
                <button
                  onClick={handlePrev}
                  className="p-2 text-[#444444] hover:bg-gray-100 transition-colors rounded-md cursor-pointer"
                  aria-label="Previous Slide"
                >
                  <ChevronLeftIcon className="w-6 h-6" />
                </button>
                <button
                  onClick={handleNext}
                  className="p-2 text-[#444444] hover:bg-gray-100 transition-colors rounded-md cursor-pointer"
                  aria-label="Next Slide"
                >
                  <ChevronRightIcon className="w-6 h-6" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};