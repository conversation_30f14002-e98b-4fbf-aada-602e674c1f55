import type { Metadata } from "next";
import { Nunito_Sans } from "next/font/google";
import "./globals.css";
import { <PERSON><PERSON>, MaintenanceBanner, Footer, StructuredData, GoogleAnalytics, AnalyticsProvider, CookieConsent } from '@/components';
import { generateMetadata, generateStructuredData } from '@/utils/seo';

const nunitoSans = Nunito_Sans({
  variable: "--font-nunito-sans",
  subsets: ["latin"],
  weight: ["300", "400", "600", "700", "800"],
});

export const metadata: Metadata = generateMetadata();

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <GoogleAnalytics />
      </head>
      <body className={`${nunitoSans.variable} antialiased`}>
        <AnalyticsProvider>
          <StructuredData data={generateStructuredData.organization()} />
          <StructuredData data={generateStructuredData.website()} />
          <Header />
          {/* <MaintenanceBanner /> */}
          {children}
          <Footer />
          <CookieConsent />
        </AnalyticsProvider>
      </body>
    </html>
  );
}
